"""
数据提取服务 - 处理复杂关联关系的高效数据提取
"""
from sqlalchemy.orm import Session, joinedload, selectinload, contains_eager
from sqlalchemy import and_, or_, func, text
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime, timedelta
import json
from loguru import logger

from ..models.database_models import (
    Article, ArticleDetail, ArticleStats, Channel, Author, 
    Tag, Category, Comment, UserInteraction
)

class DataExtractor:
    """数据提取器 - 优化复杂关联查询"""
    
    def __init__(self, db_session: Session):
        self.db_session = db_session
    
    def get_articles_for_processing(
        self, 
        batch_size: int = 1000, 
        offset: int = 0,
        include_processed: bool = False
    ) -> List[Dict[str, Any]]:
        """
        获取用于处理的文章数据（包含所有关联数据）
        
        Args:
            batch_size: 批次大小
            offset: 偏移量
            include_processed: 是否包含已处理的文章
        
        Returns:
            包含完整关联数据的文章列表
        """
        try:
            # 构建基础查询，使用 joinedload 预加载关联数据
            query = self.db_session.query(Article)\
                .options(
                    # 一对一关系 - 使用 joinedload
                    joinedload(Article.article_detail),
                    joinedload(Article.article_stats),
                    
                    # 多对一关系 - 使用 joinedload
                    joinedload(Article.channel),
                    joinedload(Article.author),
                    
                    # 多对多关系 - 使用 selectinload（避免笛卡尔积）
                    selectinload(Article.tags),
                    selectinload(Article.categories),
                    
                    # 一对多关系 - 使用 selectinload
                    selectinload(Article.comments),
                    selectinload(Article.user_interactions)
                )
            
            # 添加过滤条件
            if not include_processed:
                query = query.filter(Article.is_processed == False)
            
            # 只获取已发布的文章
            query = query.filter(Article.status == 'published')
            
            # 分页
            articles = query.offset(offset).limit(batch_size).all()
            
            # 转换为字典格式
            result = []
            for article in articles:
                article_data = self._convert_article_to_dict(article)
                result.append(article_data)
            
            logger.info(f"成功提取 {len(result)} 篇文章数据")
            return result
            
        except Exception as e:
            logger.error(f"提取文章数据失败: {e}")
            raise
    
    def get_single_article_complete(self, article_id: int) -> Optional[Dict[str, Any]]:
        """
        获取单篇文章的完整数据
        """
        try:
            article = self.db_session.query(Article)\
                .options(
                    joinedload(Article.article_detail),
                    joinedload(Article.article_stats),
                    joinedload(Article.channel),
                    joinedload(Article.author),
                    selectinload(Article.tags),
                    selectinload(Article.categories),
                    selectinload(Article.comments),
                    selectinload(Article.user_interactions)
                )\
                .filter(Article.id == article_id)\
                .first()
            
            if article:
                return self._convert_article_to_dict(article)
            return None
            
        except Exception as e:
            logger.error(f"获取文章 {article_id} 数据失败: {e}")
            raise
    
    def get_articles_by_channel(
        self, 
        channel_id: int, 
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        按频道获取文章数据
        """
        try:
            articles = self.db_session.query(Article)\
                .options(
                    joinedload(Article.article_detail),
                    joinedload(Article.channel),
                    joinedload(Article.author),
                    selectinload(Article.tags),
                    selectinload(Article.categories)
                )\
                .filter(Article.channel_id == channel_id)\
                .filter(Article.status == 'published')\
                .order_by(Article.publish_time.desc())\
                .limit(limit)\
                .all()
            
            return [self._convert_article_to_dict(article) for article in articles]
            
        except Exception as e:
            logger.error(f"按频道 {channel_id} 获取文章失败: {e}")
            raise
    
    def get_user_interaction_summary(
        self, 
        article_ids: List[int]
    ) -> Dict[int, Dict[str, Any]]:
        """
        批量获取文章的用户交互统计
        """
        try:
            # 使用原生SQL进行聚合查询，提高性能
            sql = text("""
                SELECT 
                    article_id,
                    interaction_type,
                    COUNT(*) as count,
                    AVG(reading_duration) as avg_reading_duration,
                    AVG(scroll_depth) as avg_scroll_depth
                FROM user_interactions 
                WHERE article_id IN :article_ids
                GROUP BY article_id, interaction_type
            """)
            
            result = self.db_session.execute(sql, {"article_ids": tuple(article_ids)})
            
            # 组织数据
            interaction_summary = {}
            for row in result:
                article_id = row.article_id
                if article_id not in interaction_summary:
                    interaction_summary[article_id] = {}
                
                interaction_summary[article_id][row.interaction_type] = {
                    'count': row.count,
                    'avg_reading_duration': float(row.avg_reading_duration or 0),
                    'avg_scroll_depth': float(row.avg_scroll_depth or 0)
                }
            
            return interaction_summary
            
        except Exception as e:
            logger.error(f"获取用户交互统计失败: {e}")
            raise
    
    def _convert_article_to_dict(self, article: Article) -> Dict[str, Any]:
        """
        将 SQLAlchemy 对象转换为字典
        """
        # 基础文章信息
        article_dict = {
            'id': article.id,
            'title': article.title,
            'content': article.content,
            'summary': article.summary,
            'like_count': article.like_count,
            'comment_count': article.comment_count,
            'collect_count': article.collect_count,
            'view_count': article.view_count,
            'share_count': article.share_count,
            'publish_time': article.publish_time.isoformat() if article.publish_time else None,
            'created_at': article.created_at.isoformat() if article.created_at else None,
            'status': article.status,
            'is_processed': article.is_processed
        }
        
        # 文章详情（一对一）
        if article.article_detail:
            article_dict['detail'] = {
                'meta_description': article.article_detail.meta_description,
                'meta_keywords': article.article_detail.meta_keywords,
                'reading_time': article.article_detail.reading_time,
                'word_count': article.article_detail.word_count,
                'image_count': article.article_detail.image_count,
                'video_count': article.article_detail.video_count,
                'external_links': article.article_detail.external_links
            }
        
        # 文章统计（一对一）
        if article.article_stats:
            article_dict['stats'] = {
                'avg_reading_time': article.article_stats.avg_reading_time,
                'bounce_rate': article.article_stats.bounce_rate,
                'completion_rate': article.article_stats.completion_rate,
                'engagement_score': article.article_stats.engagement_score,
                'quality_score': article.article_stats.quality_score,
                'last_calculated': article.article_stats.last_calculated.isoformat() if article.article_stats.last_calculated else None
            }
        
        # 频道信息（多对一）
        if article.channel:
            article_dict['channel'] = {
                'id': article.channel.id,
                'name': article.channel.name,
                'description': article.channel.description,
                'parent_id': article.channel.parent_id
            }
        
        # 作者信息（多对一）
        if article.author:
            article_dict['author'] = {
                'id': article.author.id,
                'name': article.author.name,
                'email': article.author.email,
                'bio': article.author.bio,
                'follower_count': article.author.follower_count,
                'article_count': article.author.article_count
            }
        
        # 标签（多对多）
        if article.tags:
            article_dict['tags'] = [
                {
                    'id': tag.id,
                    'name': tag.name,
                    'description': tag.description,
                    'usage_count': tag.usage_count
                }
                for tag in article.tags
            ]
        
        # 分类/亚专业（多对多）
        if article.categories:
            article_dict['categories'] = [
                {
                    'id': category.id,
                    'name': category.name,
                    'parent_id': category.parent_id,
                    'level': category.level
                }
                for category in article.categories
            ]
        
        # 评论统计（一对多 - 只统计数量和最新几条）
        if article.comments:
            article_dict['comments_summary'] = {
                'total_count': len(article.comments),
                'recent_comments': [
                    {
                        'id': comment.id,
                        'user_id': comment.user_id,
                        'content': comment.content[:100] + '...' if len(comment.content) > 100 else comment.content,
                        'like_count': comment.like_count,
                        'created_at': comment.created_at.isoformat() if comment.created_at else None
                    }
                    for comment in sorted(article.comments, key=lambda x: x.created_at, reverse=True)[:5]
                ]
            }
        
        # 用户交互统计（一对多 - 聚合统计）
        if article.user_interactions:
            interactions_by_type = {}
            total_reading_duration = 0
            total_scroll_depth = 0
            interaction_count = 0
            
            for interaction in article.user_interactions:
                interaction_type = interaction.interaction_type
                if interaction_type not in interactions_by_type:
                    interactions_by_type[interaction_type] = 0
                interactions_by_type[interaction_type] += 1
                
                if interaction.reading_duration:
                    total_reading_duration += interaction.reading_duration
                    interaction_count += 1
                
                if interaction.scroll_depth:
                    total_scroll_depth += interaction.scroll_depth
            
            article_dict['interactions_summary'] = {
                'by_type': interactions_by_type,
                'avg_reading_duration': total_reading_duration / interaction_count if interaction_count > 0 else 0,
                'avg_scroll_depth': total_scroll_depth / len(article.user_interactions) if article.user_interactions else 0,
                'total_interactions': len(article.user_interactions)
            }
        
        return article_dict
    
    def mark_articles_as_processed(self, article_ids: List[int]) -> bool:
        """
        标记文章为已处理
        """
        try:
            self.db_session.query(Article)\
                .filter(Article.id.in_(article_ids))\
                .update({Article.is_processed: True}, synchronize_session=False)
            
            self.db_session.commit()
            logger.info(f"成功标记 {len(article_ids)} 篇文章为已处理")
            return True
            
        except Exception as e:
            self.db_session.rollback()
            logger.error(f"标记文章为已处理失败: {e}")
            return False
