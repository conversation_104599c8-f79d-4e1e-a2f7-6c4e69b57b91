"""
数据库模型定义 - 支持复杂关联关系
"""
from sqlalchemy import (
    create_engine, Column, Integer, String, Text, DateTime, 
    Float, Boolean, ForeignKey, Table, JSON
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, sessionmaker, joinedload, selectinload
from sqlalchemy.sql import func
from datetime import datetime
from typing import List, Optional
import json

Base = declarative_base()

# 多对多关联表示例
article_tags_association = Table(
    'article_tags',
    Base.metadata,
    Column('article_id', Integer, ForeignKey('articles.id'), primary_key=True),
    Column('tag_id', Integer, ForeignKey('tags.id'), primary_key=True)
)

article_categories_association = Table(
    'article_categories', 
    Base.metadata,
    Column('article_id', Integer, ForeignKey('articles.id'), primary_key=True),
    Column('category_id', Integer, ForeignKey('categories.id'), primary_key=True)
)

class Article(Base):
    """文章主表"""
    __tablename__ = 'articles'
    
    id = Column(Integer, primary_key=True)
    title = Column(String(500), nullable=False)
    content = Column(Text)  # HTML内容
    summary = Column(Text)  # 摘要
    channel_id = Column(Integer, ForeignKey('channels.id'))
    author_id = Column(Integer, ForeignKey('authors.id'))
    like_count = Column(Integer, default=0)
    comment_count = Column(Integer, default=0)
    collect_count = Column(Integer, default=0)
    view_count = Column(Integer, default=0)
    share_count = Column(Integer, default=0)
    publish_time = Column(DateTime, default=datetime.utcnow)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    status = Column(String(20), default='published')  # published, draft, deleted
    is_processed = Column(Boolean, default=False)  # 是否已处理
    
    # 一对一关系
    article_detail = relationship("ArticleDetail", back_populates="article", uselist=False)
    article_stats = relationship("ArticleStats", back_populates="article", uselist=False)
    
    # 一对多关系
    comments = relationship("Comment", back_populates="article")
    user_interactions = relationship("UserInteraction", back_populates="article")
    
    # 多对一关系
    channel = relationship("Channel", back_populates="articles")
    author = relationship("Author", back_populates="articles")
    
    # 多对多关系
    tags = relationship("Tag", secondary=article_tags_association, back_populates="articles")
    categories = relationship("Category", secondary=article_categories_association, back_populates="articles")

class ArticleDetail(Base):
    """文章详情表 - 一对一"""
    __tablename__ = 'article_details'
    
    id = Column(Integer, primary_key=True)
    article_id = Column(Integer, ForeignKey('articles.id'), unique=True)
    meta_description = Column(Text)
    meta_keywords = Column(String(500))
    reading_time = Column(Integer)  # 预估阅读时间（分钟）
    word_count = Column(Integer)
    image_count = Column(Integer)
    video_count = Column(Integer)
    external_links = Column(JSON)  # 外部链接列表
    
    # 反向关系
    article = relationship("Article", back_populates="article_detail")

class ArticleStats(Base):
    """文章统计表 - 一对一"""
    __tablename__ = 'article_stats'
    
    id = Column(Integer, primary_key=True)
    article_id = Column(Integer, ForeignKey('articles.id'), unique=True)
    avg_reading_time = Column(Float)  # 平均阅读时间
    bounce_rate = Column(Float)  # 跳出率
    completion_rate = Column(Float)  # 完读率
    engagement_score = Column(Float)  # 参与度评分
    quality_score = Column(Float)  # 质量评分
    last_calculated = Column(DateTime, default=datetime.utcnow)
    
    # 反向关系
    article = relationship("Article", back_populates="article_stats")

class Channel(Base):
    """频道表"""
    __tablename__ = 'channels'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    parent_id = Column(Integer, ForeignKey('channels.id'))  # 支持层级结构
    sort_order = Column(Integer, default=0)
    is_active = Column(Boolean, default=True)
    
    # 自关联关系
    parent = relationship("Channel", remote_side=[id])
    children = relationship("Channel")
    
    # 一对多关系
    articles = relationship("Article", back_populates="channel")

class Author(Base):
    """作者表"""
    __tablename__ = 'authors'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    email = Column(String(200))
    bio = Column(Text)
    avatar_url = Column(String(500))
    follower_count = Column(Integer, default=0)
    article_count = Column(Integer, default=0)
    total_likes = Column(Integer, default=0)
    
    # 一对多关系
    articles = relationship("Article", back_populates="author")

class Tag(Base):
    """标签表"""
    __tablename__ = 'tags'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False, unique=True)
    description = Column(Text)
    usage_count = Column(Integer, default=0)
    
    # 多对多关系
    articles = relationship("Article", secondary=article_tags_association, back_populates="tags")

class Category(Base):
    """分类表（亚专业）"""
    __tablename__ = 'categories'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
    parent_id = Column(Integer, ForeignKey('categories.id'))
    level = Column(Integer, default=1)  # 分类层级
    
    # 自关联关系
    parent = relationship("Category", remote_side=[id])
    children = relationship("Category")
    
    # 多对多关系
    articles = relationship("Article", secondary=article_categories_association, back_populates="categories")

class Comment(Base):
    """评论表 - 一对多"""
    __tablename__ = 'comments'
    
    id = Column(Integer, primary_key=True)
    article_id = Column(Integer, ForeignKey('articles.id'))
    user_id = Column(Integer)
    content = Column(Text)
    like_count = Column(Integer, default=0)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 多对一关系
    article = relationship("Article", back_populates="comments")

class UserInteraction(Base):
    """用户交互表 - 一对多"""
    __tablename__ = 'user_interactions'
    
    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, nullable=False)
    article_id = Column(Integer, ForeignKey('articles.id'))
    interaction_type = Column(String(20))  # like, collect, share, view
    interaction_time = Column(DateTime, default=datetime.utcnow)
    reading_duration = Column(Integer)  # 阅读时长（秒）
    scroll_depth = Column(Float)  # 滚动深度百分比
    
    # 多对一关系
    article = relationship("Article", back_populates="user_interactions")

# 数据库连接配置
class DatabaseConfig:
    def __init__(self, host: str, port: int, username: str, password: str, database: str):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.database = database
        
        # 构建连接字符串
        self.connection_string = (
            f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}"
            "?charset=utf8mb4"
        )
        
        # 创建引擎
        self.engine = create_engine(
            self.connection_string,
            pool_size=10,
            max_overflow=20,
            pool_pre_ping=True,
            echo=False  # 设置为True可以看到SQL语句
        )
        
        # 创建会话工厂
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def get_session(self):
        """获取数据库会话"""
        return self.SessionLocal()
    
    def create_tables(self):
        """创建所有表"""
        Base.metadata.create_all(bind=self.engine)
