"""
数据提取使用示例
"""
import os
from dotenv import load_dotenv
from loguru import logger
import json
from typing import List, Dict, Any

from ..models.database_models import DatabaseConfig
from .data_extractor import DataExtractor

# 加载环境变量
load_dotenv()

class ArticleDataProcessor:
    """文章数据处理器"""
    
    def __init__(self):
        # 数据库配置
        self.db_config = DatabaseConfig(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            username=os.getenv('DB_USERNAME', 'root'),
            password=os.getenv('DB_PASSWORD', ''),
            database=os.getenv('DB_DATABASE', 'recommendation_system')
        )
        
        # 配置日志
        logger.add("logs/data_extraction.log", rotation="1 day", retention="30 days")
    
    def extract_articles_batch(
        self,
        batch_size: int = 1000,
        max_batches: Optional[int] = None,
        processing_mode: str = "lightweight"  # "full", "lightweight", "streaming"
    ) -> List[Dict[str, Any]]:
        """
        批量提取文章数据 - 支持多种处理模式

        Args:
            batch_size: 每批处理的文章数量
            max_batches: 最大批次数（None表示处理所有）
            processing_mode: 处理模式
                - "full": 全量数据加载（原方案）
                - "lightweight": 轻量级处理，只加载必要数据
                - "streaming": 流式处理，逐条处理

        Returns:
            所有提取的文章数据
        """
        if processing_mode == "lightweight":
            return self._extract_articles_lightweight(batch_size, max_batches)
        elif processing_mode == "streaming":
            return self._extract_articles_streaming(batch_size, max_batches)
        else:
            return self._extract_articles_full(batch_size, max_batches)

    def _extract_articles_lightweight(self, batch_size: int, max_batches: Optional[int]) -> List[Dict[str, Any]]:
        """
        轻量级数据提取 - 只获取核心数据，按需查询关联数据
        """
        all_articles = []
        batch_count = 0
        offset = 0

        with self.db_config.get_session() as session:
            while True:
                logger.info(f"轻量级处理第 {batch_count + 1} 批数据")

                # 只查询核心字段，不加载关联数据
                articles = self._get_core_articles_data(session, batch_size, offset)

                if not articles:
                    break

                # 批量获取关联数据（更高效）
                article_ids = [article['id'] for article in articles]

                # 分别批量查询关联数据
                categories_map = self._get_articles_categories_batch(session, article_ids)
                tags_map = self._get_articles_tags_batch(session, article_ids)
                stats_map = self._get_articles_stats_batch(session, article_ids)

                # 组装数据
                for article in articles:
                    article_id = article['id']
                    article['categories'] = categories_map.get(article_id, [])
                    article['tags'] = tags_map.get(article_id, [])
                    article['stats'] = stats_map.get(article_id, {})

                    # 数据清洗和特征提取
                    processed_article = self._process_single_article_lightweight(article)
                    all_articles.append(processed_article)

                # 标记为已处理
                self._mark_articles_processed(session, article_ids)

                logger.info(f"轻量级处理完成，处理了 {len(articles)} 篇文章")

                batch_count += 1
                offset += batch_size

                if max_batches and batch_count >= max_batches:
                    break

        return all_articles

    def _extract_articles_streaming(self, batch_size: int, max_batches: Optional[int]) -> List[Dict[str, Any]]:
        """
        流式处理 - 逐条处理，内存占用最小
        """
        all_articles = []
        batch_count = 0
        offset = 0

        with self.db_config.get_session() as session:
            while True:
                logger.info(f"流式处理第 {batch_count + 1} 批数据")

                # 只获取文章ID列表
                article_ids = self._get_unprocessed_article_ids(session, batch_size, offset)

                if not article_ids:
                    break

                # 逐条处理文章
                for article_id in article_ids:
                    try:
                        # 按需加载单篇文章数据
                        article_data = self._get_single_article_on_demand(session, article_id)

                        # 立即处理并清洗
                        processed_article = self._process_single_article_streaming(article_data)
                        all_articles.append(processed_article)

                        # 立即标记为已处理
                        self._mark_single_article_processed(session, article_id)

                    except Exception as e:
                        logger.error(f"流式处理文章 {article_id} 失败: {e}")
                        continue

                logger.info(f"流式处理完成，处理了 {len(article_ids)} 篇文章")

                batch_count += 1
                offset += batch_size

                if max_batches and batch_count >= max_batches:
                    break

        return all_articles

    def _get_core_articles_data(self, session, batch_size: int, offset: int) -> List[Dict[str, Any]]:
        """
        只获取文章核心数据，不加载关联数据
        """
        from sqlalchemy import text

        sql = text("""
            SELECT
                a.id, a.title, a.content, a.channel_id, a.author_id,
                a.like_count, a.comment_count, a.collect_count,
                a.view_count, a.share_count, a.publish_time,
                a.status, a.is_processed,
                c.name as channel_name,
                au.name as author_name
            FROM articles a
            LEFT JOIN channels c ON a.channel_id = c.id
            LEFT JOIN authors au ON a.author_id = au.id
            WHERE a.is_processed = 0 AND a.status = 'published'
            ORDER BY a.id
            LIMIT :limit OFFSET :offset
        """)

        result = session.execute(sql, {"limit": batch_size, "offset": offset})

        articles = []
        for row in result:
            articles.append({
                'id': row.id,
                'title': row.title,
                'content': row.content,
                'channel_id': row.channel_id,
                'author_id': row.author_id,
                'like_count': row.like_count,
                'comment_count': row.comment_count,
                'collect_count': row.collect_count,
                'view_count': row.view_count,
                'share_count': row.share_count,
                'publish_time': row.publish_time.isoformat() if row.publish_time else None,
                'status': row.status,
                'is_processed': row.is_processed,
                'channel_name': row.channel_name,
                'author_name': row.author_name
            })

        return articles

    def _get_articles_categories_batch(self, session, article_ids: List[int]) -> Dict[int, List[Dict]]:
        """
        批量获取文章分类数据
        """
        from sqlalchemy import text

        sql = text("""
            SELECT ac.article_id, c.id, c.name, c.parent_id, c.level
            FROM article_categories ac
            JOIN categories c ON ac.category_id = c.id
            WHERE ac.article_id IN :article_ids
        """)

        result = session.execute(sql, {"article_ids": tuple(article_ids)})

        categories_map = {}
        for row in result:
            article_id = row.article_id
            if article_id not in categories_map:
                categories_map[article_id] = []

            categories_map[article_id].append({
                'id': row.id,
                'name': row.name,
                'parent_id': row.parent_id,
                'level': row.level
            })

        return categories_map

    def _get_articles_tags_batch(self, session, article_ids: List[int]) -> Dict[int, List[Dict]]:
        """
        批量获取文章标签数据
        """
        from sqlalchemy import text

        sql = text("""
            SELECT at.article_id, t.id, t.name, t.usage_count
            FROM article_tags at
            JOIN tags t ON at.tag_id = t.id
            WHERE at.article_id IN :article_ids
        """)

        result = session.execute(sql, {"article_ids": tuple(article_ids)})

        tags_map = {}
        for row in result:
            article_id = row.article_id
            if article_id not in tags_map:
                tags_map[article_id] = []

            tags_map[article_id].append({
                'id': row.id,
                'name': row.name,
                'usage_count': row.usage_count
            })

        return tags_map

    def _get_articles_stats_batch(self, session, article_ids: List[int]) -> Dict[int, Dict]:
        """
        批量获取文章统计数据
        """
        from sqlalchemy import text

        sql = text("""
            SELECT article_id, avg_reading_time, bounce_rate,
                   completion_rate, engagement_score, quality_score
            FROM article_stats
            WHERE article_id IN :article_ids
        """)

        result = session.execute(sql, {"article_ids": tuple(article_ids)})

        stats_map = {}
        for row in result:
            stats_map[row.article_id] = {
                'avg_reading_time': float(row.avg_reading_time or 0),
                'bounce_rate': float(row.bounce_rate or 0),
                'completion_rate': float(row.completion_rate or 0),
                'engagement_score': float(row.engagement_score or 0),
                'quality_score': float(row.quality_score or 0)
            }

        return stats_map

    def _process_articles_batch(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理一批文章数据
        """
        processed_articles = []
        
        for article in articles:
            try:
                # 数据清洗和预处理
                processed_article = self._clean_article_data(article)
                
                # 提取特征数据
                features = self._extract_features(processed_article)
                processed_article['features'] = features
                
                processed_articles.append(processed_article)
                
            except Exception as e:
                logger.error(f"处理文章 {article.get('id')} 失败: {e}")
                continue
        
        return processed_articles
    
    def _clean_article_data(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗文章数据
        """
        # 创建副本避免修改原数据
        cleaned_article = article.copy()
        
        # 处理HTML内容
        if cleaned_article.get('content'):
            # 这里可以添加HTML清洗逻辑
            # cleaned_article['cleaned_content'] = clean_html(article['content'])
            pass
        
        # 处理标题
        if cleaned_article.get('title'):
            cleaned_article['title'] = cleaned_article['title'].strip()
        
        # 处理分类数据
        if cleaned_article.get('categories'):
            # 提取分类名称列表
            category_names = [cat['name'] for cat in cleaned_article['categories']]
            cleaned_article['category_names'] = category_names
        
        # 处理标签数据
        if cleaned_article.get('tags'):
            # 提取标签名称列表
            tag_names = [tag['name'] for tag in cleaned_article['tags']]
            cleaned_article['tag_names'] = tag_names
        
        return cleaned_article
    
    def _extract_features(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取文章特征
        """
        features = {}
        
        # 基础特征
        features['content_length'] = len(article.get('content', ''))
        features['title_length'] = len(article.get('title', ''))
        
        # 互动特征
        features['total_interactions'] = (
            article.get('like_count', 0) + 
            article.get('comment_count', 0) + 
            article.get('collect_count', 0)
        )
        
        # 质量特征
        if article.get('stats'):
            features['quality_score'] = article['stats'].get('quality_score', 0)
            features['engagement_score'] = article['stats'].get('engagement_score', 0)
        
        # 分类特征
        features['category_count'] = len(article.get('categories', []))
        features['tag_count'] = len(article.get('tags', []))
        
        # 作者特征
        if article.get('author'):
            features['author_follower_count'] = article['author'].get('follower_count', 0)
            features['author_article_count'] = article['author'].get('article_count', 0)
        
        return features
    
    def get_channel_statistics(self) -> Dict[str, Any]:
        """
        获取频道统计信息
        """
        with self.db_config.get_session() as session:
            extractor = DataExtractor(session)
            
            # 这里可以添加频道统计查询
            # 例如：每个频道的文章数量、平均质量评分等
            pass
    
    def export_to_json(self, articles: List[Dict[str, Any]], filename: str):
        """
        导出数据到JSON文件
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已导出到 {filename}")
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")

# 使用示例
def main():
    """主函数示例"""
    processor = ArticleDataProcessor()
    
    # 批量提取文章数据
    articles = processor.extract_articles_batch(
        batch_size=500,  # 每批500篇文章
        max_batches=10   # 最多处理10批
    )
    
    # 导出数据
    processor.export_to_json(articles, 'extracted_articles.json')
    
    logger.info(f"处理完成，共提取 {len(articles)} 篇文章")

if __name__ == "__main__":
    main()
