"""
数据提取使用示例
"""
import os
from dotenv import load_dotenv
from loguru import logger
import json
from typing import List, Dict, Any

from ..models.database_models import DatabaseConfig
from .data_extractor import DataExtractor

# 加载环境变量
load_dotenv()

class ArticleDataProcessor:
    """文章数据处理器"""
    
    def __init__(self):
        # 数据库配置
        self.db_config = DatabaseConfig(
            host=os.getenv('DB_HOST', 'localhost'),
            port=int(os.getenv('DB_PORT', 3306)),
            username=os.getenv('DB_USERNAME', 'root'),
            password=os.getenv('DB_PASSWORD', ''),
            database=os.getenv('DB_DATABASE', 'recommendation_system')
        )
        
        # 配置日志
        logger.add("logs/data_extraction.log", rotation="1 day", retention="30 days")
    
    def extract_articles_batch(
        self, 
        batch_size: int = 1000,
        max_batches: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        批量提取文章数据
        
        Args:
            batch_size: 每批处理的文章数量
            max_batches: 最大批次数（None表示处理所有）
        
        Returns:
            所有提取的文章数据
        """
        all_articles = []
        batch_count = 0
        offset = 0
        
        with self.db_config.get_session() as session:
            extractor = DataExtractor(session)
            
            while True:
                logger.info(f"开始处理第 {batch_count + 1} 批数据，偏移量: {offset}")
                
                # 提取一批数据
                articles = extractor.get_articles_for_processing(
                    batch_size=batch_size,
                    offset=offset,
                    include_processed=False
                )
                
                if not articles:
                    logger.info("没有更多数据需要处理")
                    break
                
                # 处理数据
                processed_articles = self._process_articles_batch(articles)
                all_articles.extend(processed_articles)
                
                # 标记为已处理
                article_ids = [article['id'] for article in articles]
                extractor.mark_articles_as_processed(article_ids)
                
                logger.info(f"第 {batch_count + 1} 批处理完成，处理了 {len(articles)} 篇文章")
                
                # 更新计数器
                batch_count += 1
                offset += batch_size
                
                # 检查是否达到最大批次数
                if max_batches and batch_count >= max_batches:
                    logger.info(f"达到最大批次数 {max_batches}，停止处理")
                    break
        
        logger.info(f"批量处理完成，总共处理了 {len(all_articles)} 篇文章")
        return all_articles
    
    def _process_articles_batch(self, articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        处理一批文章数据
        """
        processed_articles = []
        
        for article in articles:
            try:
                # 数据清洗和预处理
                processed_article = self._clean_article_data(article)
                
                # 提取特征数据
                features = self._extract_features(processed_article)
                processed_article['features'] = features
                
                processed_articles.append(processed_article)
                
            except Exception as e:
                logger.error(f"处理文章 {article.get('id')} 失败: {e}")
                continue
        
        return processed_articles
    
    def _clean_article_data(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗文章数据
        """
        # 创建副本避免修改原数据
        cleaned_article = article.copy()
        
        # 处理HTML内容
        if cleaned_article.get('content'):
            # 这里可以添加HTML清洗逻辑
            # cleaned_article['cleaned_content'] = clean_html(article['content'])
            pass
        
        # 处理标题
        if cleaned_article.get('title'):
            cleaned_article['title'] = cleaned_article['title'].strip()
        
        # 处理分类数据
        if cleaned_article.get('categories'):
            # 提取分类名称列表
            category_names = [cat['name'] for cat in cleaned_article['categories']]
            cleaned_article['category_names'] = category_names
        
        # 处理标签数据
        if cleaned_article.get('tags'):
            # 提取标签名称列表
            tag_names = [tag['name'] for tag in cleaned_article['tags']]
            cleaned_article['tag_names'] = tag_names
        
        return cleaned_article
    
    def _extract_features(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取文章特征
        """
        features = {}
        
        # 基础特征
        features['content_length'] = len(article.get('content', ''))
        features['title_length'] = len(article.get('title', ''))
        
        # 互动特征
        features['total_interactions'] = (
            article.get('like_count', 0) + 
            article.get('comment_count', 0) + 
            article.get('collect_count', 0)
        )
        
        # 质量特征
        if article.get('stats'):
            features['quality_score'] = article['stats'].get('quality_score', 0)
            features['engagement_score'] = article['stats'].get('engagement_score', 0)
        
        # 分类特征
        features['category_count'] = len(article.get('categories', []))
        features['tag_count'] = len(article.get('tags', []))
        
        # 作者特征
        if article.get('author'):
            features['author_follower_count'] = article['author'].get('follower_count', 0)
            features['author_article_count'] = article['author'].get('article_count', 0)
        
        return features
    
    def get_channel_statistics(self) -> Dict[str, Any]:
        """
        获取频道统计信息
        """
        with self.db_config.get_session() as session:
            extractor = DataExtractor(session)
            
            # 这里可以添加频道统计查询
            # 例如：每个频道的文章数量、平均质量评分等
            pass
    
    def export_to_json(self, articles: List[Dict[str, Any]], filename: str):
        """
        导出数据到JSON文件
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(articles, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已导出到 {filename}")
            
        except Exception as e:
            logger.error(f"导出数据失败: {e}")

# 使用示例
def main():
    """主函数示例"""
    processor = ArticleDataProcessor()
    
    # 批量提取文章数据
    articles = processor.extract_articles_batch(
        batch_size=500,  # 每批500篇文章
        max_batches=10   # 最多处理10批
    )
    
    # 导出数据
    processor.export_to_json(articles, 'extracted_articles.json')
    
    logger.info(f"处理完成，共提取 {len(articles)} 篇文章")

if __name__ == "__main__":
    main()
